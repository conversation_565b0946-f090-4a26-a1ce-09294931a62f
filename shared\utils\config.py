"""
Configuration management utilities for the Multi-Agent Development System.

This module provides centralized configuration management with support for
environment variables, YAML files, and runtime configuration updates.
"""

import os
import yaml
from pathlib import Path
from typing import Any, Dict, List, Optional, Union
from dataclasses import dataclass, field

from pydantic import BaseSettings, Field
from pydantic_settings import SettingsConfigDict

from ..models.enums import LLMProvider, LogLevel, DeploymentEnvironment


class SystemConfig(BaseSettings):
    """System-wide configuration settings."""
    
    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=False,
        extra="allow"
    )
    
    # Environment
    environment: DeploymentEnvironment = Field(default=DeploymentEnvironment.DEVELOPMENT)
    debug: bool = Field(default=True)
    testing: bool = Field(default=False)
    
    # Logging
    log_level: LogLevel = Field(default=LogLevel.INFO)
    log_format: str = Field(default="colored")  # "json" or "colored"
    log_file_path: str = Field(default="./logs/agents.log")
    log_max_size_mb: int = Field(default=100)
    log_backup_count: int = Field(default=5)
    
    # Database
    redis_host: str = Field(default="localhost")
    redis_port: int = Field(default=6379)
    redis_password: str = Field(default="")
    redis_db: int = Field(default=0)
    redis_url: Optional[str] = Field(default=None)
    
    postgres_host: str = Field(default="localhost")
    postgres_port: int = Field(default=5432)
    postgres_db: str = Field(default="multi_agent_system")
    postgres_user: str = Field(default="agent_user")
    postgres_password: str = Field(default="agent_password")
    database_url: Optional[str] = Field(default=None)
    
    # Vector Database
    chroma_persist_directory: str = Field(default="./data/chroma")
    chroma_collection_name: str = Field(default="agent_memory")
    
    # GitHub Integration
    github_token: Optional[str] = Field(default=None)
    github_username: Optional[str] = Field(default=None)
    github_repo_owner: Optional[str] = Field(default=None)
    github_repo_name: str = Field(default="multi-agent-projects")
    
    # Agent Configuration
    agent_max_retries: int = Field(default=3)
    agent_timeout_seconds: int = Field(default=30)
    agent_memory_limit: int = Field(default=1000)
    agent_log_level: LogLevel = Field(default=LogLevel.INFO)
    
    # Communication
    websocket_host: str = Field(default="localhost")
    websocket_port: int = Field(default=8765)
    websocket_path: str = Field(default="/ws")
    message_queue_max_size: int = Field(default=1000)
    message_retention_hours: int = Field(default=24)
    
    # Web Dashboard
    api_host: str = Field(default="localhost")
    api_port: int = Field(default=8080)
    api_reload: bool = Field(default=True)
    api_debug: bool = Field(default=True)
    dashboard_title: str = Field(default="Multi-Agent Development System")
    dashboard_version: str = Field(default="1.0.0")
    dashboard_secret_key: str = Field(default="your_secret_key_here")
    
    # Security
    jwt_secret_key: str = Field(default="your_jwt_secret_key_here")
    jwt_algorithm: str = Field(default="HS256")
    jwt_expiration_hours: int = Field(default=24)
    rate_limit_per_minute: int = Field(default=60)
    rate_limit_burst: int = Field(default=10)
    
    # Monitoring
    metrics_enabled: bool = Field(default=True)
    metrics_port: int = Field(default=9090)
    prometheus_endpoint: str = Field(default="/metrics")
    health_check_interval_seconds: int = Field(default=30)
    health_check_timeout_seconds: int = Field(default=5)
    
    # Performance
    max_concurrent_agents: int = Field(default=10)
    max_concurrent_tasks: int = Field(default=50)
    worker_pool_size: int = Field(default=4)
    cache_ttl_seconds: int = Field(default=3600)
    cache_max_size: int = Field(default=1000)
    
    # Code Execution
    code_execution_timeout: int = Field(default=60)
    code_execution_memory_limit: int = Field(default=512)
    sandbox_enabled: bool = Field(default=True)
    docker_host: str = Field(default="unix:///var/run/docker.sock")
    docker_network: str = Field(default="multi-agent-network")
    
    # Feature Flags
    enable_vector_memory: bool = Field(default=True)
    enable_github_integration: bool = Field(default=True)
    enable_code_execution: bool = Field(default=True)
    enable_metrics: bool = Field(default=True)
    enable_web_dashboard: bool = Field(default=True)
    enable_multi_modal: bool = Field(default=False)
    enable_voice_interface: bool = Field(default=False)
    enable_advanced_planning: bool = Field(default=False)
    
    # Backup
    backup_enabled: bool = Field(default=True)
    backup_interval_hours: int = Field(default=6)
    backup_retention_days: int = Field(default=30)
    backup_storage_path: str = Field(default="./backups")


class LLMConfig(BaseSettings):
    """LLM provider configuration."""
    
    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=False
    )
    
    # OpenAI
    openai_api_key: Optional[str] = Field(default=None)
    openai_org_id: Optional[str] = Field(default=None)
    openai_default_model: str = Field(default="gpt-4-turbo")
    
    # Anthropic
    anthropic_api_key: Optional[str] = Field(default=None)
    anthropic_default_model: str = Field(default="claude-3-sonnet-20240229")
    
    # Ollama
    ollama_base_url: str = Field(default="http://localhost:11434")
    ollama_default_model: str = Field(default="llama2")
    
    # Provider Priority
    llm_provider_priority: str = Field(default="openai,anthropic,ollama")
    
    def get_provider_priority(self) -> List[LLMProvider]:
        """Get LLM provider priority as enum list."""
        providers = []
        for provider_str in self.llm_provider_priority.split(","):
            provider_str = provider_str.strip().lower()
            try:
                provider = LLMProvider(provider_str)
                providers.append(provider)
            except ValueError:
                continue
        return providers


class AgentConfig(BaseSettings):
    """Agent-specific configuration."""
    
    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=False
    )
    
    # PM Agent
    pm_agent_model: str = Field(default="gpt-4-turbo")
    pm_agent_temperature: float = Field(default=0.3)
    pm_agent_max_tokens: int = Field(default=2000)
    
    # Dev Agent
    dev_agent_model: str = Field(default="claude-3-sonnet-20240229")
    dev_agent_temperature: float = Field(default=0.1)
    dev_agent_max_tokens: int = Field(default=4000)
    
    # Test Agent
    test_agent_model: str = Field(default="gpt-4")
    test_agent_temperature: float = Field(default=0.2)
    test_agent_max_tokens: int = Field(default=1500)


@dataclass
class ConfigManager:
    """Centralized configuration manager."""
    
    system: SystemConfig = field(default_factory=SystemConfig)
    llm: LLMConfig = field(default_factory=LLMConfig)
    agents: AgentConfig = field(default_factory=AgentConfig)
    custom: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        """Initialize configuration manager."""
        self._load_yaml_configs()
    
    def _load_yaml_configs(self) -> None:
        """Load configuration from YAML files."""
        config_dir = Path("config")
        
        # Load system config
        system_config_path = config_dir / "system.yaml"
        if system_config_path.exists():
            with open(system_config_path, 'r') as f:
                system_data = yaml.safe_load(f)
                if system_data:
                    self.custom.update(system_data)
        
        # Load agent configs
        agents_config_path = config_dir / "agents.yaml"
        if agents_config_path.exists():
            with open(agents_config_path, 'r') as f:
                agents_data = yaml.safe_load(f)
                if agents_data:
                    self.custom.update(agents_data)
        
        # Load LLM config
        llm_config_path = config_dir / "llm_config.yaml"
        if llm_config_path.exists():
            with open(llm_config_path, 'r') as f:
                llm_data = yaml.safe_load(f)
                if llm_data:
                    self.custom.update(llm_data)
    
    def get(self, key: str, default: Any = None) -> Any:
        """Get configuration value by key."""
        # Try custom config first
        if key in self.custom:
            return self.custom[key]
        
        # Try system config
        if hasattr(self.system, key):
            return getattr(self.system, key)
        
        # Try LLM config
        if hasattr(self.llm, key):
            return getattr(self.llm, key)
        
        # Try agents config
        if hasattr(self.agents, key):
            return getattr(self.agents, key)
        
        return default
    
    def set(self, key: str, value: Any) -> None:
        """Set configuration value."""
        self.custom[key] = value
    
    def update(self, config_dict: Dict[str, Any]) -> None:
        """Update configuration with dictionary."""
        self.custom.update(config_dict)
    
    def get_agent_config(self, agent_role: str) -> Dict[str, Any]:
        """Get configuration for a specific agent role."""
        agent_key = f"{agent_role.lower()}_agent"
        config = {}
        
        # Get model
        model_key = f"{agent_key}_model"
        if hasattr(self.agents, model_key):
            config['model'] = getattr(self.agents, model_key)
        
        # Get temperature
        temp_key = f"{agent_key}_temperature"
        if hasattr(self.agents, temp_key):
            config['temperature'] = getattr(self.agents, temp_key)
        
        # Get max tokens
        tokens_key = f"{agent_key}_max_tokens"
        if hasattr(self.agents, tokens_key):
            config['max_tokens'] = getattr(self.agents, tokens_key)
        
        # Add custom config if exists
        if agent_key in self.custom:
            config.update(self.custom[agent_key])
        
        return config
    
    def get_database_url(self, db_type: str = "postgres") -> str:
        """Get database URL."""
        if db_type == "postgres":
            if self.system.database_url:
                return self.system.database_url
            return (
                f"postgresql://{self.system.postgres_user}:{self.system.postgres_password}"
                f"@{self.system.postgres_host}:{self.system.postgres_port}/{self.system.postgres_db}"
            )
        elif db_type == "redis":
            if self.system.redis_url:
                return self.system.redis_url
            return f"redis://{self.system.redis_host}:{self.system.redis_port}/{self.system.redis_db}"
        else:
            raise ValueError(f"Unknown database type: {db_type}")
    
    def is_feature_enabled(self, feature: str) -> bool:
        """Check if a feature is enabled."""
        feature_key = f"enable_{feature}"
        return self.get(feature_key, False)
    
    def get_llm_config(self, provider: LLMProvider) -> Dict[str, Any]:
        """Get LLM configuration for a provider."""
        config = {}
        
        if provider == LLMProvider.OPENAI:
            config = {
                'api_key': self.llm.openai_api_key,
                'org_id': self.llm.openai_org_id,
                'default_model': self.llm.openai_default_model,
            }
        elif provider == LLMProvider.ANTHROPIC:
            config = {
                'api_key': self.llm.anthropic_api_key,
                'default_model': self.llm.anthropic_default_model,
            }
        elif provider == LLMProvider.OLLAMA:
            config = {
                'base_url': self.llm.ollama_base_url,
                'default_model': self.llm.ollama_default_model,
            }
        
        return config
    
    def validate(self) -> List[str]:
        """Validate configuration and return list of issues."""
        issues = []
        
        # Check required API keys
        if self.is_feature_enabled("github_integration") and not self.system.github_token:
            issues.append("GitHub token is required when GitHub integration is enabled")
        
        # Check LLM API keys
        providers = self.llm.get_provider_priority()
        has_valid_provider = False
        
        for provider in providers:
            llm_config = self.get_llm_config(provider)
            if provider == LLMProvider.OPENAI and llm_config.get('api_key'):
                has_valid_provider = True
                break
            elif provider == LLMProvider.ANTHROPIC and llm_config.get('api_key'):
                has_valid_provider = True
                break
            elif provider == LLMProvider.OLLAMA:
                has_valid_provider = True  # Ollama doesn't require API key
                break
        
        if not has_valid_provider:
            issues.append("At least one LLM provider must be configured with valid credentials")
        
        # Check directories
        required_dirs = [
            self.system.log_file_path,
            self.system.chroma_persist_directory,
            self.system.backup_storage_path,
        ]
        
        for dir_path in required_dirs:
            dir_obj = Path(dir_path).parent
            if not dir_obj.exists():
                try:
                    dir_obj.mkdir(parents=True, exist_ok=True)
                except Exception as e:
                    issues.append(f"Cannot create directory {dir_obj}: {e}")
        
        return issues
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary."""
        return {
            'system': self.system.dict(),
            'llm': self.llm.dict(),
            'agents': self.agents.dict(),
            'custom': self.custom,
        }


# Global configuration manager
config = ConfigManager()

# Convenience functions
def get_config(key: str, default: Any = None) -> Any:
    """Get configuration value."""
    return config.get(key, default)


def set_config(key: str, value: Any) -> None:
    """Set configuration value."""
    config.set(key, value)


def get_agent_config(agent_role: str) -> Dict[str, Any]:
    """Get agent configuration."""
    return config.get_agent_config(agent_role)


def get_database_url(db_type: str = "postgres") -> str:
    """Get database URL."""
    return config.get_database_url(db_type)


def is_feature_enabled(feature: str) -> bool:
    """Check if feature is enabled."""
    return config.is_feature_enabled(feature)


def validate_config() -> List[str]:
    """Validate configuration."""
    return config.validate()
