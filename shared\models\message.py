"""
Message model definitions for agent communication.

This module defines the data structures used for communication between agents
in the multi-agent development system.
"""

from datetime import datetime
from typing import Any, Dict, List, Optional, Union
from uuid import uuid4

from pydantic import BaseModel, Field, validator

from .enums import MessageType, AgentRole


class MessageMetadata(BaseModel):
    """Metadata for messages."""
    
    priority: int = Field(default=1, ge=1, le=5, description="Message priority (1-5)")
    requires_response: bool = Field(default=False, description="Whether message requires a response")
    response_timeout: Optional[int] = Field(default=None, description="Response timeout in seconds")
    thread_id: Optional[str] = Field(default=None, description="Thread ID for conversation tracking")
    correlation_id: Optional[str] = Field(default=None, description="Correlation ID for request tracking")
    retry_count: int = Field(default=0, description="Number of retry attempts")
    max_retries: int = Field(default=3, description="Maximum retry attempts")
    tags: List[str] = Field(default_factory=list, description="Message tags for categorization")
    context: Dict[str, Any] = Field(default_factory=dict, description="Additional context data")


class Message(BaseModel):
    """Core message structure for agent communication."""
    
    id: str = Field(default_factory=lambda: str(uuid4()), description="Unique message identifier")
    sender_id: str = Field(..., description="ID of the sending agent")
    sender_role: AgentRole = Field(..., description="Role of the sending agent")
    receiver_id: Optional[str] = Field(default=None, description="ID of the receiving agent (None for broadcast)")
    receiver_role: Optional[AgentRole] = Field(default=None, description="Role of the receiving agent")
    message_type: MessageType = Field(..., description="Type of the message")
    content: str = Field(..., description="Message content")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="Message timestamp")
    metadata: MessageMetadata = Field(default_factory=MessageMetadata, description="Message metadata")
    
    class Config:
        """Pydantic configuration."""
        json_encoders = {
            datetime: lambda v: v.isoformat(),
        }
        use_enum_values = True
    
    @validator('content')
    def content_not_empty(cls, v):
        """Validate that content is not empty."""
        if not v or not v.strip():
            raise ValueError('Message content cannot be empty')
        return v.strip()
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert message to dictionary."""
        return self.dict()
    
    def is_broadcast(self) -> bool:
        """Check if message is a broadcast message."""
        return self.receiver_id is None
    
    def is_direct(self) -> bool:
        """Check if message is a direct message."""
        return self.receiver_id is not None
    
    def requires_response(self) -> bool:
        """Check if message requires a response."""
        return self.metadata.requires_response
    
    def is_expired(self) -> bool:
        """Check if message response timeout has expired."""
        if not self.metadata.response_timeout:
            return False
        
        elapsed = (datetime.utcnow() - self.timestamp).total_seconds()
        return elapsed > self.metadata.response_timeout
    
    def can_retry(self) -> bool:
        """Check if message can be retried."""
        return self.metadata.retry_count < self.metadata.max_retries
    
    def increment_retry(self) -> None:
        """Increment retry count."""
        self.metadata.retry_count += 1
    
    def add_tag(self, tag: str) -> None:
        """Add a tag to the message."""
        if tag not in self.metadata.tags:
            self.metadata.tags.append(tag)
    
    def has_tag(self, tag: str) -> bool:
        """Check if message has a specific tag."""
        return tag in self.metadata.tags
    
    def set_context(self, key: str, value: Any) -> None:
        """Set context data."""
        self.metadata.context[key] = value
    
    def get_context(self, key: str, default: Any = None) -> Any:
        """Get context data."""
        return self.metadata.context.get(key, default)


class TaskMessage(Message):
    """Specialized message for task-related communication."""
    
    task_id: str = Field(..., description="ID of the related task")
    action: str = Field(..., description="Action to be performed")
    parameters: Dict[str, Any] = Field(default_factory=dict, description="Action parameters")
    
    def __init__(self, **data):
        """Initialize task message."""
        data['message_type'] = MessageType.TASK
        super().__init__(**data)


class ResponseMessage(Message):
    """Specialized message for responses."""
    
    original_message_id: str = Field(..., description="ID of the original message")
    success: bool = Field(..., description="Whether the operation was successful")
    result: Optional[Dict[str, Any]] = Field(default=None, description="Operation result")
    error: Optional[str] = Field(default=None, description="Error message if operation failed")
    
    def __init__(self, **data):
        """Initialize response message."""
        data['message_type'] = MessageType.RESPONSE
        super().__init__(**data)


class NotificationMessage(Message):
    """Specialized message for notifications."""
    
    notification_type: str = Field(..., description="Type of notification")
    severity: str = Field(default="info", description="Notification severity")
    data: Dict[str, Any] = Field(default_factory=dict, description="Notification data")
    
    def __init__(self, **data):
        """Initialize notification message."""
        data['message_type'] = MessageType.NOTIFICATION
        super().__init__(**data)


class BroadcastMessage(Message):
    """Specialized message for broadcast communication."""
    
    target_roles: Optional[List[AgentRole]] = Field(default=None, description="Target agent roles")
    exclude_agents: List[str] = Field(default_factory=list, description="Agent IDs to exclude")
    
    def __init__(self, **data):
        """Initialize broadcast message."""
        data['message_type'] = MessageType.BROADCAST
        data['receiver_id'] = None  # Ensure broadcast messages have no specific receiver
        super().__init__(**data)
    
    def should_receive(self, agent_id: str, agent_role: AgentRole) -> bool:
        """Check if an agent should receive this broadcast message."""
        # Don't send to sender
        if agent_id == self.sender_id:
            return False
        
        # Don't send to excluded agents
        if agent_id in self.exclude_agents:
            return False
        
        # If target roles specified, check if agent role is included
        if self.target_roles:
            return agent_role in self.target_roles
        
        return True


class StatusUpdateMessage(Message):
    """Specialized message for status updates."""
    
    status: str = Field(..., description="New status")
    previous_status: Optional[str] = Field(default=None, description="Previous status")
    details: Dict[str, Any] = Field(default_factory=dict, description="Status details")
    
    def __init__(self, **data):
        """Initialize status update message."""
        data['message_type'] = MessageType.STATUS_UPDATE
        super().__init__(**data)


class ErrorMessage(Message):
    """Specialized message for error reporting."""
    
    error_code: str = Field(..., description="Error code")
    error_type: str = Field(..., description="Error type")
    stack_trace: Optional[str] = Field(default=None, description="Stack trace")
    context: Dict[str, Any] = Field(default_factory=dict, description="Error context")
    
    def __init__(self, **data):
        """Initialize error message."""
        data['message_type'] = MessageType.ERROR
        super().__init__(**data)


class HeartbeatMessage(Message):
    """Specialized message for heartbeat/health checks."""
    
    health_status: str = Field(..., description="Health status")
    metrics: Dict[str, Any] = Field(default_factory=dict, description="Health metrics")
    
    def __init__(self, **data):
        """Initialize heartbeat message."""
        data['message_type'] = MessageType.HEARTBEAT
        super().__init__(**data)


# Message factory functions
def create_task_message(
    sender_id: str,
    sender_role: AgentRole,
    task_id: str,
    action: str,
    receiver_id: Optional[str] = None,
    receiver_role: Optional[AgentRole] = None,
    parameters: Optional[Dict[str, Any]] = None,
    **kwargs
) -> TaskMessage:
    """Create a task message."""
    return TaskMessage(
        sender_id=sender_id,
        sender_role=sender_role,
        receiver_id=receiver_id,
        receiver_role=receiver_role,
        task_id=task_id,
        action=action,
        parameters=parameters or {},
        content=f"Task {action} for {task_id}",
        **kwargs
    )


def create_response_message(
    sender_id: str,
    sender_role: AgentRole,
    original_message: Message,
    success: bool,
    result: Optional[Dict[str, Any]] = None,
    error: Optional[str] = None,
    **kwargs
) -> ResponseMessage:
    """Create a response message."""
    return ResponseMessage(
        sender_id=sender_id,
        sender_role=sender_role,
        receiver_id=original_message.sender_id,
        receiver_role=original_message.sender_role,
        original_message_id=original_message.id,
        success=success,
        result=result,
        error=error,
        content=f"Response to {original_message.id}: {'Success' if success else 'Failed'}",
        **kwargs
    )


def create_notification_message(
    sender_id: str,
    sender_role: AgentRole,
    notification_type: str,
    content: str,
    severity: str = "info",
    data: Optional[Dict[str, Any]] = None,
    **kwargs
) -> NotificationMessage:
    """Create a notification message."""
    return NotificationMessage(
        sender_id=sender_id,
        sender_role=sender_role,
        notification_type=notification_type,
        content=content,
        severity=severity,
        data=data or {},
        **kwargs
    )
