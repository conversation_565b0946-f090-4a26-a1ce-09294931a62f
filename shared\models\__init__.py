"""
Shared data models for the Multi-Agent Development System.

This module provides all the data models and enums used throughout the system
for consistent data representation and type safety.
"""

# Import all enums
from .enums import (
    AgentStatus,
    AgentRole,
    MessageType,
    TaskStatus,
    TaskPriority,
    TaskType,
    LLMProvider,
    CommunicationChannel,
    LogLevel,
    ToolType,
    ProjectStatus,
    CodeQuality,
    TestResult,
    MemoryType,
    EventType,
    HealthStatus,
    DeploymentEnvironment,
    # Utility functions
    get_enum_values,
    get_enum_names,
    is_valid_enum_value,
    can_transition_agent_status,
    can_transition_task_status,
    get_priority_value,
    compare_priorities,
    # Constants
    AGENT_STATUS_TRANSITIONS,
    TASK_STATUS_TRANSITIONS,
    PRIORITY_ORDER,
)

# Import message models
from .message import (
    Message,
    MessageMetadata,
    TaskMessage,
    ResponseMessage,
    NotificationMessage,
    BroadcastMessage,
    StatusUpdateMessage,
    ErrorMessage,
    HeartbeatMessage,
    # Factory functions
    create_task_message,
    create_response_message,
    create_notification_message,
)

# Import task models
from .task import (
    Task,
    TaskMetadata,
    TaskRequirements,
    TaskResult,
    # Factory functions
    create_task,
    create_subtask,
)

# Import agent models
from .agent import (
    Agent,
    AgentCapabilities,
    AgentConfiguration,
    AgentMetrics,
    AgentState,
    # Factory functions
    create_agent,
)

# Version information
__version__ = "1.0.0"
__author__ = "inkbytefo"