"""
Task model definitions for the multi-agent development system.

This module defines the data structures used for task management and execution
across different agents in the system.
"""

from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Set
from uuid import uuid4

from pydantic import BaseModel, Field, validator

from .enums import TaskStatus, TaskPriority, TaskType, AgentRole


class TaskMetadata(BaseModel):
    """Metadata for tasks."""
    
    tags: List[str] = Field(default_factory=list, description="Task tags for categorization")
    labels: Dict[str, str] = Field(default_factory=dict, description="Key-value labels")
    context: Dict[str, Any] = Field(default_factory=dict, description="Additional context data")
    metrics: Dict[str, float] = Field(default_factory=dict, description="Task metrics")
    attachments: List[str] = Field(default_factory=list, description="File attachments")
    links: List[str] = Field(default_factory=list, description="Related links")


class TaskRequirements(BaseModel):
    """Task requirements and constraints."""
    
    required_skills: List[str] = Field(default_factory=list, description="Required skills")
    required_tools: List[str] = Field(default_factory=list, description="Required tools")
    required_agents: List[AgentRole] = Field(default_factory=list, description="Required agent roles")
    memory_limit_mb: Optional[int] = Field(default=None, description="Memory limit in MB")
    cpu_limit_percent: Optional[int] = Field(default=None, description="CPU limit percentage")
    timeout_seconds: Optional[int] = Field(default=None, description="Task timeout in seconds")
    max_retries: int = Field(default=3, description="Maximum retry attempts")


class TaskResult(BaseModel):
    """Task execution result."""
    
    success: bool = Field(..., description="Whether task completed successfully")
    output: Optional[Dict[str, Any]] = Field(default=None, description="Task output data")
    error: Optional[str] = Field(default=None, description="Error message if failed")
    logs: List[str] = Field(default_factory=list, description="Execution logs")
    metrics: Dict[str, float] = Field(default_factory=dict, description="Execution metrics")
    artifacts: List[str] = Field(default_factory=list, description="Generated artifacts")
    duration_seconds: Optional[float] = Field(default=None, description="Execution duration")
    
    @validator('success')
    def validate_success_error(cls, v, values):
        """Validate that failed tasks have error messages."""
        if not v and not values.get('error'):
            raise ValueError('Failed tasks must have an error message')
        return v


class Task(BaseModel):
    """Core task structure for the multi-agent system."""
    
    id: str = Field(default_factory=lambda: str(uuid4()), description="Unique task identifier")
    title: str = Field(..., description="Task title")
    description: str = Field(..., description="Detailed task description")
    task_type: TaskType = Field(..., description="Type of task")
    status: TaskStatus = Field(default=TaskStatus.PENDING, description="Current task status")
    priority: TaskPriority = Field(default=TaskPriority.MEDIUM, description="Task priority")
    
    # Assignment and ownership
    assigned_to: Optional[str] = Field(default=None, description="ID of assigned agent")
    assigned_role: Optional[AgentRole] = Field(default=None, description="Role of assigned agent")
    created_by: str = Field(..., description="ID of agent that created the task")
    created_by_role: AgentRole = Field(..., description="Role of agent that created the task")
    
    # Dependencies and relationships
    dependencies: List[str] = Field(default_factory=list, description="Task dependencies (task IDs)")
    dependents: List[str] = Field(default_factory=list, description="Tasks that depend on this one")
    parent_task_id: Optional[str] = Field(default=None, description="Parent task ID")
    subtasks: List[str] = Field(default_factory=list, description="Subtask IDs")
    
    # Timing
    created_at: datetime = Field(default_factory=datetime.utcnow, description="Creation timestamp")
    updated_at: datetime = Field(default_factory=datetime.utcnow, description="Last update timestamp")
    started_at: Optional[datetime] = Field(default=None, description="Start timestamp")
    completed_at: Optional[datetime] = Field(default=None, description="Completion timestamp")
    deadline: Optional[datetime] = Field(default=None, description="Task deadline")
    estimated_duration: Optional[timedelta] = Field(default=None, description="Estimated duration")
    
    # Execution details
    requirements: TaskRequirements = Field(default_factory=TaskRequirements, description="Task requirements")
    result: Optional[TaskResult] = Field(default=None, description="Task execution result")
    retry_count: int = Field(default=0, description="Number of retry attempts")
    
    # Additional data
    metadata: TaskMetadata = Field(default_factory=TaskMetadata, description="Task metadata")
    
    class Config:
        """Pydantic configuration."""
        json_encoders = {
            datetime: lambda v: v.isoformat(),
            timedelta: lambda v: v.total_seconds(),
        }
        use_enum_values = True
    
    @validator('title')
    def title_not_empty(cls, v):
        """Validate that title is not empty."""
        if not v or not v.strip():
            raise ValueError('Task title cannot be empty')
        return v.strip()
    
    @validator('description')
    def description_not_empty(cls, v):
        """Validate that description is not empty."""
        if not v or not v.strip():
            raise ValueError('Task description cannot be empty')
        return v.strip()
    
    @validator('updated_at', always=True)
    def set_updated_at(cls, v):
        """Always update the updated_at timestamp."""
        return datetime.utcnow()
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert task to dictionary."""
        return self.dict()
    
    def is_pending(self) -> bool:
        """Check if task is pending."""
        return self.status == TaskStatus.PENDING
    
    def is_assigned(self) -> bool:
        """Check if task is assigned."""
        return self.status == TaskStatus.ASSIGNED
    
    def is_in_progress(self) -> bool:
        """Check if task is in progress."""
        return self.status == TaskStatus.IN_PROGRESS
    
    def is_completed(self) -> bool:
        """Check if task is completed."""
        return self.status == TaskStatus.COMPLETED
    
    def is_failed(self) -> bool:
        """Check if task is failed."""
        return self.status == TaskStatus.FAILED
    
    def is_cancelled(self) -> bool:
        """Check if task is cancelled."""
        return self.status == TaskStatus.CANCELLED
    
    def is_blocked(self) -> bool:
        """Check if task is blocked."""
        return self.status == TaskStatus.BLOCKED
    
    def is_terminal(self) -> bool:
        """Check if task is in a terminal state."""
        return self.status in [TaskStatus.COMPLETED, TaskStatus.CANCELLED, TaskStatus.APPROVED]
    
    def can_start(self) -> bool:
        """Check if task can be started."""
        return self.status in [TaskStatus.PENDING, TaskStatus.ASSIGNED]
    
    def can_retry(self) -> bool:
        """Check if task can be retried."""
        return (
            self.status == TaskStatus.FAILED and 
            self.retry_count < self.requirements.max_retries
        )
    
    def is_overdue(self) -> bool:
        """Check if task is overdue."""
        if not self.deadline:
            return False
        return datetime.utcnow() > self.deadline
    
    def get_duration(self) -> Optional[timedelta]:
        """Get actual task duration."""
        if not self.started_at:
            return None
        
        end_time = self.completed_at or datetime.utcnow()
        return end_time - self.started_at
    
    def get_remaining_time(self) -> Optional[timedelta]:
        """Get remaining time until deadline."""
        if not self.deadline:
            return None
        
        remaining = self.deadline - datetime.utcnow()
        return remaining if remaining.total_seconds() > 0 else timedelta(0)
    
    def add_dependency(self, task_id: str) -> None:
        """Add a task dependency."""
        if task_id not in self.dependencies:
            self.dependencies.append(task_id)
    
    def remove_dependency(self, task_id: str) -> None:
        """Remove a task dependency."""
        if task_id in self.dependencies:
            self.dependencies.remove(task_id)
    
    def add_subtask(self, task_id: str) -> None:
        """Add a subtask."""
        if task_id not in self.subtasks:
            self.subtasks.append(task_id)
    
    def remove_subtask(self, task_id: str) -> None:
        """Remove a subtask."""
        if task_id in self.subtasks:
            self.subtasks.remove(task_id)
    
    def assign_to(self, agent_id: str, agent_role: AgentRole) -> None:
        """Assign task to an agent."""
        self.assigned_to = agent_id
        self.assigned_role = agent_role
        if self.status == TaskStatus.PENDING:
            self.status = TaskStatus.ASSIGNED
    
    def start(self) -> None:
        """Start task execution."""
        if self.can_start():
            self.status = TaskStatus.IN_PROGRESS
            self.started_at = datetime.utcnow()
    
    def complete(self, result: TaskResult) -> None:
        """Complete the task with a result."""
        self.status = TaskStatus.COMPLETED
        self.completed_at = datetime.utcnow()
        self.result = result
    
    def fail(self, error: str, logs: Optional[List[str]] = None) -> None:
        """Mark task as failed."""
        self.status = TaskStatus.FAILED
        self.completed_at = datetime.utcnow()
        self.result = TaskResult(
            success=False,
            error=error,
            logs=logs or []
        )
    
    def cancel(self, reason: str = "Cancelled by user") -> None:
        """Cancel the task."""
        self.status = TaskStatus.CANCELLED
        self.completed_at = datetime.utcnow()
        self.result = TaskResult(
            success=False,
            error=reason
        )
    
    def block(self, reason: str) -> None:
        """Block the task."""
        self.status = TaskStatus.BLOCKED
        self.metadata.context['block_reason'] = reason
    
    def unblock(self) -> None:
        """Unblock the task."""
        if self.status == TaskStatus.BLOCKED:
            self.status = TaskStatus.ASSIGNED if self.assigned_to else TaskStatus.PENDING
            self.metadata.context.pop('block_reason', None)
    
    def retry(self) -> None:
        """Retry the task."""
        if self.can_retry():
            self.retry_count += 1
            self.status = TaskStatus.ASSIGNED if self.assigned_to else TaskStatus.PENDING
            self.started_at = None
            self.completed_at = None
            self.result = None
    
    def add_tag(self, tag: str) -> None:
        """Add a tag to the task."""
        if tag not in self.metadata.tags:
            self.metadata.tags.append(tag)
    
    def has_tag(self, tag: str) -> bool:
        """Check if task has a specific tag."""
        return tag in self.metadata.tags
    
    def set_label(self, key: str, value: str) -> None:
        """Set a label on the task."""
        self.metadata.labels[key] = value
    
    def get_label(self, key: str, default: str = None) -> Optional[str]:
        """Get a label value."""
        return self.metadata.labels.get(key, default)
    
    def set_context(self, key: str, value: Any) -> None:
        """Set context data."""
        self.metadata.context[key] = value
    
    def get_context(self, key: str, default: Any = None) -> Any:
        """Get context data."""
        return self.metadata.context.get(key, default)


# Task factory functions
def create_task(
    title: str,
    description: str,
    task_type: TaskType,
    created_by: str,
    created_by_role: AgentRole,
    priority: TaskPriority = TaskPriority.MEDIUM,
    deadline: Optional[datetime] = None,
    estimated_duration: Optional[timedelta] = None,
    **kwargs
) -> Task:
    """Create a new task."""
    return Task(
        title=title,
        description=description,
        task_type=task_type,
        created_by=created_by,
        created_by_role=created_by_role,
        priority=priority,
        deadline=deadline,
        estimated_duration=estimated_duration,
        **kwargs
    )


def create_subtask(
    parent_task: Task,
    title: str,
    description: str,
    task_type: TaskType,
    **kwargs
) -> Task:
    """Create a subtask."""
    subtask = create_task(
        title=title,
        description=description,
        task_type=task_type,
        created_by=parent_task.created_by,
        created_by_role=parent_task.created_by_role,
        parent_task_id=parent_task.id,
        **kwargs
    )

    parent_task.add_subtask(subtask.id)
    return subtask
