"""
Base Agent class for the Multi-Agent Development System.

This module provides the foundational BaseAgent class that all specialized agents
inherit from, providing common functionality for communication, task processing,
and system integration.
"""

import asyncio
import uuid
from abc import ABC, abstractmethod
from datetime import datetime
from typing import Any, Dict, List, Optional, Set, Union

from shared.models import (
    Agent, AgentRole, AgentStatus, AgentCapabilities, AgentConfiguration,
    Message, MessageType, Task, TaskStatus, TaskResult,
    create_agent, create_response_message, create_notification_message
)
from shared.utils import (
    get_agent_logger, log_agent_action, handle_error,
    AgentException, TaskException, CommunicationException,
    ErrorCode, get_config
)


class BaseAgent(ABC):
    """
    Base class for all agents in the multi-agent system.
    
    Provides common functionality including:
    - Message handling and communication
    - Task processing and management
    - LLM integration
    - Tool management
    - Memory and state management
    - Error handling and recovery
    """
    
    def __init__(
        self,
        name: str,
        role: AgentRole,
        description: str,
        capabilities: Optional[AgentCapabilities] = None,
        configuration: Optional[AgentConfiguration] = None,
        **kwargs
    ):
        """Initialize the base agent."""
        # Create agent model
        self.agent = create_agent(
            name=name,
            role=role,
            description=description,
            llm_provider=configuration.llm_provider if configuration else "openai",
            model_name=configuration.model_name if configuration else "gpt-4-turbo",
            capabilities=capabilities,
            configuration=configuration,
            **kwargs
        )
        
        # Initialize components
        self.logger = get_agent_logger(self.agent.id, self.agent.role)
        self.communication_hub = None  # Will be set by communication system
        self.llm_client = None  # Will be initialized in start()
        self.tool_manager = None  # Will be initialized in start()
        self.memory = None  # Will be initialized in start()
        
        # State management
        self._running = False
        self._task_queue: asyncio.Queue = asyncio.Queue()
        self._active_tasks: Dict[str, Task] = {}
        self._message_handlers: Dict[MessageType, callable] = {}
        self._shutdown_event = asyncio.Event()
        
        # Performance tracking
        self._start_time: Optional[datetime] = None
        self._last_heartbeat: Optional[datetime] = None
        
        # Register default message handlers
        self._register_default_handlers()
        
        self.logger.info(f"Agent {self.agent.name} ({self.agent.role.value}) initialized")
    
    @property
    def id(self) -> str:
        """Get agent ID."""
        return self.agent.id
    
    @property
    def name(self) -> str:
        """Get agent name."""
        return self.agent.name
    
    @property
    def role(self) -> AgentRole:
        """Get agent role."""
        return self.agent.role
    
    @property
    def status(self) -> AgentStatus:
        """Get current agent status."""
        return self.agent.state.status
    
    @property
    def is_running(self) -> bool:
        """Check if agent is running."""
        return self._running
    
    @property
    def is_available(self) -> bool:
        """Check if agent is available for new tasks."""
        return self.agent.is_available()
    
    def _register_default_handlers(self) -> None:
        """Register default message handlers."""
        self._message_handlers = {
            MessageType.TASK: self._handle_task_message,
            MessageType.RESPONSE: self._handle_response_message,
            MessageType.NOTIFICATION: self._handle_notification_message,
            MessageType.BROADCAST: self._handle_broadcast_message,
            MessageType.STATUS_UPDATE: self._handle_status_update_message,
            MessageType.HEARTBEAT: self._handle_heartbeat_message,
            MessageType.SYSTEM: self._handle_system_message,
        }
    
    async def start(self) -> None:
        """Start the agent."""
        try:
            self.logger.info(f"Starting agent {self.name}")
            
            # Initialize components
            await self._initialize_components()
            
            # Update agent state
            self.agent.start()
            self._running = True
            self._start_time = datetime.utcnow()
            
            # Start background tasks
            asyncio.create_task(self._task_processor())
            asyncio.create_task(self._heartbeat_loop())
            
            log_agent_action(
                self.agent.id,
                self.agent.role,
                "agent_started",
                {"timestamp": self._start_time.isoformat()}
            )
            
            self.logger.info(f"Agent {self.name} started successfully")
            
        except Exception as e:
            self.agent.set_error(f"Failed to start agent: {str(e)}")
            self.logger.error(f"Failed to start agent: {e}")
            raise AgentException(
                f"Failed to start agent {self.name}",
                agent_id=self.agent.id,
                agent_role=self.agent.role,
                error_code=ErrorCode.AGENT_INITIALIZATION_ERROR,
                cause=e
            )
    
    async def stop(self) -> None:
        """Stop the agent gracefully."""
        try:
            self.logger.info(f"Stopping agent {self.name}")
            
            # Signal shutdown
            self._running = False
            self._shutdown_event.set()
            
            # Complete active tasks
            await self._complete_active_tasks()
            
            # Update agent state
            self.agent.stop()
            
            log_agent_action(
                self.agent.id,
                self.agent.role,
                "agent_stopped",
                {"uptime_seconds": self.agent.get_uptime().total_seconds() if self.agent.get_uptime() else 0}
            )
            
            self.logger.info(f"Agent {self.name} stopped successfully")
            
        except Exception as e:
            self.logger.error(f"Error stopping agent: {e}")
            handle_error(e, {"agent_id": self.agent.id, "operation": "stop"})
    
    async def _initialize_components(self) -> None:
        """Initialize agent components."""
        # Initialize LLM client
        from shared.llm import LLMClient
        self.llm_client = LLMClient(
            provider=self.agent.configuration.llm_provider,
            model=self.agent.configuration.model_name,
            temperature=self.agent.configuration.temperature,
            max_tokens=self.agent.configuration.max_tokens
        )
        
        # Initialize tool manager
        from shared.tools import ToolManager
        self.tool_manager = ToolManager(
            agent_id=self.agent.id,
            available_tools=self.agent.capabilities.tools
        )
        
        # Initialize memory system if enabled
        if self.agent.configuration.enable_memory:
            from shared.memory import AgentMemory
            self.memory = AgentMemory(
                agent_id=self.agent.id,
                collection_name=f"agent_{self.agent.id}_memory"
            )
    
    async def _task_processor(self) -> None:
        """Process tasks from the task queue."""
        while self._running:
            try:
                # Wait for task or shutdown
                task = await asyncio.wait_for(
                    self._task_queue.get(),
                    timeout=1.0
                )
                
                if task:
                    await self._process_task_internal(task)
                    
            except asyncio.TimeoutError:
                continue
            except Exception as e:
                self.logger.error(f"Error in task processor: {e}")
                handle_error(e, {"agent_id": self.agent.id, "operation": "task_processing"})
    
    async def _heartbeat_loop(self) -> None:
        """Send periodic heartbeat messages."""
        interval = self.agent.configuration.heartbeat_interval
        
        while self._running:
            try:
                await asyncio.sleep(interval)
                
                if self._running:
                    await self._send_heartbeat()
                    
            except Exception as e:
                self.logger.error(f"Error in heartbeat loop: {e}")
    
    async def _send_heartbeat(self) -> None:
        """Send heartbeat message."""
        self.agent.update_heartbeat()
        self._last_heartbeat = datetime.utcnow()
        
        # Update metrics
        self.agent.update_metrics(
            uptime_seconds=self.agent.get_uptime().total_seconds() if self.agent.get_uptime() else 0,
            active_tasks=len(self._active_tasks)
        )
    
    async def handle_message(self, message: Message) -> Optional[Message]:
        """Handle incoming message."""
        try:
            self.logger.debug(f"Received message: {message.message_type.value} from {message.sender_id}")
            
            # Get handler for message type
            handler = self._message_handlers.get(message.message_type)
            if handler:
                response = await handler(message)
                
                # Update metrics
                self.agent.metrics.messages_received += 1
                
                return response
            else:
                self.logger.warning(f"No handler for message type: {message.message_type.value}")
                return None
                
        except Exception as e:
            self.logger.error(f"Error handling message: {e}")
            handle_error(e, {
                "agent_id": self.agent.id,
                "message_id": message.id,
                "message_type": message.message_type.value
            })
            return None
    
    async def send_message(self, message: Message) -> bool:
        """Send a message through the communication hub."""
        try:
            if self.communication_hub:
                await self.communication_hub.send_message(message)
                self.agent.metrics.messages_sent += 1
                return True
            else:
                self.logger.error("Communication hub not available")
                return False
                
        except Exception as e:
            self.logger.error(f"Error sending message: {e}")
            handle_error(e, {
                "agent_id": self.agent.id,
                "message_id": message.id,
                "operation": "send_message"
            })
            return False
    
    async def assign_task(self, task: Task) -> bool:
        """Assign a task to this agent."""
        try:
            if not self.agent.can_accept_task():
                self.logger.warning(f"Cannot accept task {task.id}: agent not available")
                return False
            
            # Assign task
            task.assign_to(self.agent.id, self.agent.role)
            
            # Add to queue
            await self._task_queue.put(task)
            
            self.logger.info(f"Task {task.id} assigned and queued")
            return True
            
        except Exception as e:
            self.logger.error(f"Error assigning task: {e}")
            handle_error(e, {
                "agent_id": self.agent.id,
                "task_id": task.id,
                "operation": "assign_task"
            })
            return False
    
    # Abstract methods that must be implemented by subclasses
    @abstractmethod
    async def process_task(self, task: Task) -> TaskResult:
        """Process a task. Must be implemented by subclasses."""
        pass
    
    @abstractmethod
    async def get_capabilities(self) -> AgentCapabilities:
        """Get agent capabilities. Must be implemented by subclasses."""
        pass
    
    # Default message handlers
    async def _handle_task_message(self, message: Message) -> Optional[Message]:
        """Handle task-related messages."""
        try:
            # Extract task information from message
            task_data = message.metadata.context.get('task_data')
            if not task_data:
                return create_response_message(
                    sender_id=self.agent.id,
                    sender_role=self.agent.role,
                    original_message=message,
                    success=False,
                    error="No task data in message"
                )

            # Create task object
            from shared.models import Task
            task = Task(**task_data)

            # Assign task
            success = await self.assign_task(task)

            return create_response_message(
                sender_id=self.agent.id,
                sender_role=self.agent.role,
                original_message=message,
                success=success,
                result={"task_id": task.id} if success else None,
                error=None if success else "Failed to assign task"
            )

        except Exception as e:
            self.logger.error(f"Error handling task message: {e}")
            return create_response_message(
                sender_id=self.agent.id,
                sender_role=self.agent.role,
                original_message=message,
                success=False,
                error=str(e)
            )

    async def _handle_response_message(self, message: Message) -> Optional[Message]:
        """Handle response messages."""
        self.logger.debug(f"Received response to message: {message.metadata.correlation_id}")
        return None

    async def _handle_notification_message(self, message: Message) -> Optional[Message]:
        """Handle notification messages."""
        self.logger.info(f"Notification: {message.content}")
        return None

    async def _handle_broadcast_message(self, message: Message) -> Optional[Message]:
        """Handle broadcast messages."""
        self.logger.info(f"Broadcast: {message.content}")
        return None

    async def _handle_status_update_message(self, message: Message) -> Optional[Message]:
        """Handle status update messages."""
        self.logger.debug(f"Status update from {message.sender_id}: {message.content}")
        return None

    async def _handle_heartbeat_message(self, message: Message) -> Optional[Message]:
        """Handle heartbeat messages."""
        return None

    async def _handle_system_message(self, message: Message) -> Optional[Message]:
        """Handle system messages."""
        self.logger.info(f"System message: {message.content}")
        return None

    # Internal task processing methods
    async def _process_task_internal(self, task: Task) -> None:
        """Internal task processing with error handling and state management."""
        try:
            self.logger.info(f"Starting task {task.id}: {task.title}")

            # Update agent and task state
            self.agent.set_working(task.id)
            task.start()
            self._active_tasks[task.id] = task

            # Process the task
            result = await self.process_task(task)

            # Complete the task
            task.complete(result)
            self.agent.complete_task(task.id, result.success)

            # Remove from active tasks
            self._active_tasks.pop(task.id, None)

            # Log completion
            log_agent_action(
                self.agent.id,
                self.agent.role,
                "task_completed",
                {
                    "task_id": task.id,
                    "success": result.success,
                    "duration_seconds": task.get_duration().total_seconds() if task.get_duration() else 0
                }
            )

            self.logger.info(f"Task {task.id} completed successfully: {result.success}")

        except Exception as e:
            self.logger.error(f"Error processing task {task.id}: {e}")

            # Mark task as failed
            task.fail(str(e))
            self.agent.complete_task(task.id, False)
            self._active_tasks.pop(task.id, None)

            # Handle error
            handle_error(TaskException(
                f"Task processing failed: {str(e)}",
                task_id=task.id,
                agent_id=self.agent.id,
                error_code=ErrorCode.TASK_EXECUTION_ERROR,
                cause=e
            ))

    async def _complete_active_tasks(self) -> None:
        """Complete or cancel all active tasks during shutdown."""
        for task_id, task in list(self._active_tasks.items()):
            try:
                if task.is_in_progress():
                    task.cancel("Agent shutting down")
                    self.agent.complete_task(task_id, False)
                    self.logger.info(f"Cancelled task {task_id} due to shutdown")
            except Exception as e:
                self.logger.error(f"Error cancelling task {task_id}: {e}")

        self._active_tasks.clear()

    # Utility methods
    async def notify_agents(self, message: str, target_roles: Optional[List[AgentRole]] = None) -> None:
        """Send notification to other agents."""
        try:
            notification = create_notification_message(
                sender_id=self.agent.id,
                sender_role=self.agent.role,
                notification_type="agent_notification",
                content=message
            )

            if target_roles:
                notification.metadata.context['target_roles'] = [role.value for role in target_roles]

            await self.send_message(notification)

        except Exception as e:
            self.logger.error(f"Error sending notification: {e}")

    def get_status_info(self) -> Dict[str, Any]:
        """Get comprehensive status information."""
        return {
            "agent_id": self.agent.id,
            "name": self.agent.name,
            "role": self.agent.role.value,
            "status": self.agent.state.status.value,
            "health": self.agent.state.health_status.value,
            "is_running": self._running,
            "is_available": self.agent.is_available(),
            "active_tasks": len(self._active_tasks),
            "uptime_seconds": self.agent.get_uptime().total_seconds() if self.agent.get_uptime() else 0,
            "last_heartbeat": self._last_heartbeat.isoformat() if self._last_heartbeat else None,
            "metrics": {
                "tasks_completed": self.agent.metrics.tasks_completed,
                "tasks_failed": self.agent.metrics.tasks_failed,
                "success_rate": self.agent.metrics.success_rate,
                "messages_sent": self.agent.metrics.messages_sent,
                "messages_received": self.agent.metrics.messages_received,
            }
        }

    def __str__(self) -> str:
        """String representation of the agent."""
        return f"{self.agent.name} ({self.agent.role.value}) - {self.agent.state.status.value}"

    def __repr__(self) -> str:
        """Detailed string representation of the agent."""
        return (
            f"BaseAgent(id={self.agent.id}, name={self.agent.name}, "
            f"role={self.agent.role.value}, status={self.agent.state.status.value})"
        )
