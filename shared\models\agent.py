"""
Agent model definitions for the multi-agent development system.

This module defines the data structures used for agent management and state tracking
in the multi-agent development system.
"""

from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Set
from uuid import uuid4

from pydantic import BaseModel, Field, validator

from .enums import AgentStatus, AgentRole, LLMProvider, ToolType, HealthStatus


class AgentCapabilities(BaseModel):
    """Agent capabilities and skills."""
    
    skills: List[str] = Field(default_factory=list, description="Agent skills")
    tools: List[ToolType] = Field(default_factory=list, description="Available tools")
    languages: List[str] = Field(default_factory=list, description="Programming languages")
    frameworks: List[str] = Field(default_factory=list, description="Frameworks and libraries")
    max_concurrent_tasks: int = Field(default=1, description="Maximum concurrent tasks")
    memory_limit_mb: int = Field(default=1024, description="Memory limit in MB")
    cpu_limit_percent: int = Field(default=100, description="CPU limit percentage")


class AgentConfiguration(BaseModel):
    """Agent configuration settings."""
    
    llm_provider: LLMProvider = Field(..., description="LLM provider")
    model_name: str = Field(..., description="LLM model name")
    temperature: float = Field(default=0.3, ge=0.0, le=2.0, description="LLM temperature")
    max_tokens: int = Field(default=2000, gt=0, description="Maximum tokens per request")
    timeout_seconds: int = Field(default=30, gt=0, description="Request timeout")
    max_retries: int = Field(default=3, ge=0, description="Maximum retry attempts")
    retry_delay_seconds: int = Field(default=1, ge=0, description="Delay between retries")
    
    # Agent-specific settings
    heartbeat_interval: int = Field(default=30, gt=0, description="Heartbeat interval in seconds")
    task_poll_interval: int = Field(default=5, gt=0, description="Task polling interval in seconds")
    memory_cleanup_interval: int = Field(default=3600, gt=0, description="Memory cleanup interval")
    log_level: str = Field(default="INFO", description="Logging level")
    
    # Feature flags
    enable_memory: bool = Field(default=True, description="Enable memory system")
    enable_tools: bool = Field(default=True, description="Enable tool usage")
    enable_collaboration: bool = Field(default=True, description="Enable collaboration")
    enable_learning: bool = Field(default=False, description="Enable learning from interactions")


class AgentMetrics(BaseModel):
    """Agent performance metrics."""
    
    tasks_completed: int = Field(default=0, description="Total tasks completed")
    tasks_failed: int = Field(default=0, description="Total tasks failed")
    total_execution_time: float = Field(default=0.0, description="Total execution time in seconds")
    average_response_time: float = Field(default=0.0, description="Average response time in seconds")
    memory_usage_mb: float = Field(default=0.0, description="Current memory usage in MB")
    cpu_usage_percent: float = Field(default=0.0, description="Current CPU usage percentage")
    
    # Communication metrics
    messages_sent: int = Field(default=0, description="Total messages sent")
    messages_received: int = Field(default=0, description="Total messages received")
    
    # Quality metrics
    success_rate: float = Field(default=0.0, ge=0.0, le=1.0, description="Task success rate")
    collaboration_score: float = Field(default=0.0, ge=0.0, le=1.0, description="Collaboration effectiveness")
    
    # Timestamps
    last_activity: Optional[datetime] = Field(default=None, description="Last activity timestamp")
    uptime_seconds: float = Field(default=0.0, description="Total uptime in seconds")
    
    def calculate_success_rate(self) -> float:
        """Calculate task success rate."""
        total_tasks = self.tasks_completed + self.tasks_failed
        if total_tasks == 0:
            return 0.0
        return self.tasks_completed / total_tasks
    
    def update_success_rate(self) -> None:
        """Update the success rate."""
        self.success_rate = self.calculate_success_rate()


class AgentState(BaseModel):
    """Current state of an agent."""
    
    status: AgentStatus = Field(default=AgentStatus.OFFLINE, description="Current status")
    current_task_id: Optional[str] = Field(default=None, description="Currently executing task ID")
    active_tasks: List[str] = Field(default_factory=list, description="List of active task IDs")
    pending_messages: int = Field(default=0, description="Number of pending messages")
    
    # Health information
    health_status: HealthStatus = Field(default=HealthStatus.UNKNOWN, description="Health status")
    last_heartbeat: Optional[datetime] = Field(default=None, description="Last heartbeat timestamp")
    error_count: int = Field(default=0, description="Recent error count")
    last_error: Optional[str] = Field(default=None, description="Last error message")
    
    # Resource usage
    memory_usage_percent: float = Field(default=0.0, ge=0.0, le=100.0, description="Memory usage percentage")
    cpu_usage_percent: float = Field(default=0.0, ge=0.0, le=100.0, description="CPU usage percentage")
    
    def is_healthy(self) -> bool:
        """Check if agent is healthy."""
        return (
            self.health_status == HealthStatus.HEALTHY and
            self.status not in [AgentStatus.ERROR, AgentStatus.OFFLINE] and
            self.error_count < 5
        )
    
    def is_available(self) -> bool:
        """Check if agent is available for new tasks."""
        return (
            self.status == AgentStatus.IDLE and
            self.is_healthy() and
            len(self.active_tasks) == 0
        )
    
    def can_accept_task(self, max_concurrent: int = 1) -> bool:
        """Check if agent can accept a new task."""
        return (
            self.is_healthy() and
            self.status in [AgentStatus.IDLE, AgentStatus.WORKING] and
            len(self.active_tasks) < max_concurrent
        )


class Agent(BaseModel):
    """Core agent model for the multi-agent system."""
    
    id: str = Field(default_factory=lambda: str(uuid4()), description="Unique agent identifier")
    name: str = Field(..., description="Agent name")
    role: AgentRole = Field(..., description="Agent role")
    description: str = Field(..., description="Agent description")
    version: str = Field(default="1.0.0", description="Agent version")
    
    # Configuration and capabilities
    capabilities: AgentCapabilities = Field(default_factory=AgentCapabilities, description="Agent capabilities")
    configuration: AgentConfiguration = Field(..., description="Agent configuration")
    
    # State and metrics
    state: AgentState = Field(default_factory=AgentState, description="Current agent state")
    metrics: AgentMetrics = Field(default_factory=AgentMetrics, description="Performance metrics")
    
    # Timestamps
    created_at: datetime = Field(default_factory=datetime.utcnow, description="Creation timestamp")
    started_at: Optional[datetime] = Field(default=None, description="Start timestamp")
    last_updated: datetime = Field(default_factory=datetime.utcnow, description="Last update timestamp")
    
    # Metadata
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")
    tags: List[str] = Field(default_factory=list, description="Agent tags")
    
    class Config:
        """Pydantic configuration."""
        json_encoders = {
            datetime: lambda v: v.isoformat(),
        }
        use_enum_values = True
    
    @validator('name')
    def name_not_empty(cls, v):
        """Validate that name is not empty."""
        if not v or not v.strip():
            raise ValueError('Agent name cannot be empty')
        return v.strip()
    
    @validator('last_updated', always=True)
    def set_last_updated(cls, v):
        """Always update the last_updated timestamp."""
        return datetime.utcnow()
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert agent to dictionary."""
        return self.dict()
    
    def is_online(self) -> bool:
        """Check if agent is online."""
        return self.state.status != AgentStatus.OFFLINE
    
    def is_idle(self) -> bool:
        """Check if agent is idle."""
        return self.state.status == AgentStatus.IDLE
    
    def is_working(self) -> bool:
        """Check if agent is working."""
        return self.state.status == AgentStatus.WORKING
    
    def is_available(self) -> bool:
        """Check if agent is available for new tasks."""
        return self.state.is_available()
    
    def can_accept_task(self) -> bool:
        """Check if agent can accept a new task."""
        return self.state.can_accept_task(self.capabilities.max_concurrent_tasks)
    
    def get_uptime(self) -> Optional[timedelta]:
        """Get agent uptime."""
        if not self.started_at:
            return None
        return datetime.utcnow() - self.started_at
    
    def start(self) -> None:
        """Start the agent."""
        self.state.status = AgentStatus.INITIALIZING
        self.started_at = datetime.utcnow()
        self.state.last_heartbeat = datetime.utcnow()
    
    def stop(self) -> None:
        """Stop the agent."""
        self.state.status = AgentStatus.SHUTTING_DOWN
    
    def set_idle(self) -> None:
        """Set agent to idle state."""
        self.state.status = AgentStatus.IDLE
        self.state.current_task_id = None
    
    def set_working(self, task_id: str) -> None:
        """Set agent to working state."""
        self.state.status = AgentStatus.WORKING
        self.state.current_task_id = task_id
        if task_id not in self.state.active_tasks:
            self.state.active_tasks.append(task_id)
    
    def set_waiting(self) -> None:
        """Set agent to waiting state."""
        self.state.status = AgentStatus.WAITING
    
    def set_error(self, error: str) -> None:
        """Set agent to error state."""
        self.state.status = AgentStatus.ERROR
        self.state.last_error = error
        self.state.error_count += 1
    
    def clear_error(self) -> None:
        """Clear error state."""
        if self.state.status == AgentStatus.ERROR:
            self.state.status = AgentStatus.IDLE
            self.state.error_count = 0
            self.state.last_error = None
    
    def complete_task(self, task_id: str, success: bool = True) -> None:
        """Mark a task as completed."""
        if task_id in self.state.active_tasks:
            self.state.active_tasks.remove(task_id)
        
        if self.state.current_task_id == task_id:
            self.state.current_task_id = None
            self.set_idle()
        
        # Update metrics
        if success:
            self.metrics.tasks_completed += 1
        else:
            self.metrics.tasks_failed += 1
        
        self.metrics.update_success_rate()
        self.metrics.last_activity = datetime.utcnow()
    
    def update_heartbeat(self) -> None:
        """Update heartbeat timestamp."""
        self.state.last_heartbeat = datetime.utcnow()
        self.state.health_status = HealthStatus.HEALTHY
    
    def update_metrics(self, **kwargs) -> None:
        """Update agent metrics."""
        for key, value in kwargs.items():
            if hasattr(self.metrics, key):
                setattr(self.metrics, key, value)
        
        self.metrics.last_activity = datetime.utcnow()
    
    def add_tag(self, tag: str) -> None:
        """Add a tag to the agent."""
        if tag not in self.tags:
            self.tags.append(tag)
    
    def has_tag(self, tag: str) -> bool:
        """Check if agent has a specific tag."""
        return tag in self.tags
    
    def set_metadata(self, key: str, value: Any) -> None:
        """Set metadata."""
        self.metadata[key] = value
    
    def get_metadata(self, key: str, default: Any = None) -> Any:
        """Get metadata."""
        return self.metadata.get(key, default)
    
    def has_skill(self, skill: str) -> bool:
        """Check if agent has a specific skill."""
        return skill in self.capabilities.skills
    
    def has_tool(self, tool: ToolType) -> bool:
        """Check if agent has a specific tool."""
        return tool in self.capabilities.tools
    
    def supports_language(self, language: str) -> bool:
        """Check if agent supports a programming language."""
        return language.lower() in [lang.lower() for lang in self.capabilities.languages]


# Agent factory functions
def create_agent(
    name: str,
    role: AgentRole,
    description: str,
    llm_provider: LLMProvider,
    model_name: str,
    **kwargs
) -> Agent:
    """Create a new agent."""
    configuration = AgentConfiguration(
        llm_provider=llm_provider,
        model_name=model_name,
        **kwargs.get('configuration', {})
    )
    
    return Agent(
        name=name,
        role=role,
        description=description,
        configuration=configuration,
        **{k: v for k, v in kwargs.items() if k != 'configuration'}
    )
