"""
Enum definitions for the Multi-Agent Development System.

This module contains all the enumeration types used throughout the system
for consistent state management and type safety.
"""

from enum import Enum, auto
from typing import List


class AgentStatus(Enum):
    """Agent operational status."""
    IDLE = "idle"
    WORKING = "working"
    WAITING = "waiting"
    ERROR = "error"
    OFFLINE = "offline"
    INITIALIZING = "initializing"
    SHUTTING_DOWN = "shutting_down"


class AgentRole(Enum):
    """Agent role definitions."""
    PROJECT_MANAGER = "project_manager"
    DEVELOPER = "developer"
    TESTER = "tester"
    REVIEWER = "reviewer"
    DOCUMENTER = "documenter"
    DEBUGGER = "debugger"
    COORDINATOR = "coordinator"


class MessageType(Enum):
    """Message type classifications."""
    TASK = "task"
    RESPONSE = "response"
    NOTIFICATION = "notification"
    BROADCAST = "broadcast"
    DIRECT = "direct"
    STATUS_UPDATE = "status_update"
    ERROR = "error"
    HEARTBEAT = "heartbeat"
    SYSTEM = "system"


class TaskStatus(Enum):
    """Task execution status."""
    PENDING = "pending"
    ASSIGNED = "assigned"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    BLOCKED = "blocked"
    REVIEW = "review"
    APPROVED = "approved"
    REJECTED = "rejected"


class TaskPriority(Enum):
    """Task priority levels."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    URGENT = "urgent"
    CRITICAL = "critical"


class TaskType(Enum):
    """Task type classifications."""
    PLANNING = "planning"
    DEVELOPMENT = "development"
    TESTING = "testing"
    REVIEW = "review"
    DOCUMENTATION = "documentation"
    DEBUGGING = "debugging"
    DEPLOYMENT = "deployment"
    RESEARCH = "research"
    REFACTORING = "refactoring"


class LLMProvider(Enum):
    """LLM provider options."""
    OPENAI = "openai"
    ANTHROPIC = "anthropic"
    OLLAMA = "ollama"
    HUGGINGFACE = "huggingface"
    COHERE = "cohere"


class CommunicationChannel(Enum):
    """Communication channel types."""
    WEBSOCKET = "websocket"
    REDIS = "redis"
    HTTP = "http"
    DIRECT = "direct"
    BROADCAST = "broadcast"


class LogLevel(Enum):
    """Logging levels."""
    DEBUG = "debug"
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


class ToolType(Enum):
    """Tool type classifications."""
    CODE_GENERATOR = "code_generator"
    CODE_ANALYZER = "code_analyzer"
    TEST_RUNNER = "test_runner"
    GITHUB_CLIENT = "github_client"
    FILE_MANAGER = "file_manager"
    DOCUMENTATION_GENERATOR = "documentation_generator"
    DEBUGGER = "debugger"
    PLANNER = "planner"
    VALIDATOR = "validator"


class ProjectStatus(Enum):
    """Project status classifications."""
    PLANNING = "planning"
    IN_DEVELOPMENT = "in_development"
    TESTING = "testing"
    REVIEW = "review"
    DEPLOYMENT = "deployment"
    COMPLETED = "completed"
    CANCELLED = "cancelled"
    ON_HOLD = "on_hold"


class CodeQuality(Enum):
    """Code quality levels."""
    EXCELLENT = "excellent"
    GOOD = "good"
    ACCEPTABLE = "acceptable"
    NEEDS_IMPROVEMENT = "needs_improvement"
    POOR = "poor"


class TestResult(Enum):
    """Test execution results."""
    PASSED = "passed"
    FAILED = "failed"
    SKIPPED = "skipped"
    ERROR = "error"
    TIMEOUT = "timeout"


class MemoryType(Enum):
    """Memory storage types."""
    SHORT_TERM = "short_term"
    LONG_TERM = "long_term"
    EPISODIC = "episodic"
    SEMANTIC = "semantic"
    PROCEDURAL = "procedural"


class EventType(Enum):
    """System event types."""
    AGENT_STARTED = "agent_started"
    AGENT_STOPPED = "agent_stopped"
    TASK_CREATED = "task_created"
    TASK_COMPLETED = "task_completed"
    MESSAGE_SENT = "message_sent"
    ERROR_OCCURRED = "error_occurred"
    SYSTEM_ALERT = "system_alert"


class HealthStatus(Enum):
    """Health check status."""
    HEALTHY = "healthy"
    UNHEALTHY = "unhealthy"
    WARNING = "warning"
    CRITICAL = "critical"
    UNKNOWN = "unknown"


class DeploymentEnvironment(Enum):
    """Deployment environment types."""
    DEVELOPMENT = "development"
    TESTING = "testing"
    STAGING = "staging"
    PRODUCTION = "production"
    LOCAL = "local"


# Utility functions for enum operations
def get_enum_values(enum_class: type[Enum]) -> List[str]:
    """Get all values from an enum class."""
    return [item.value for item in enum_class]


def get_enum_names(enum_class: type[Enum]) -> List[str]:
    """Get all names from an enum class."""
    return [item.name for item in enum_class]


def is_valid_enum_value(enum_class: type[Enum], value: str) -> bool:
    """Check if a value is valid for an enum class."""
    return value in get_enum_values(enum_class)


# Enum mappings for validation and conversion
AGENT_STATUS_TRANSITIONS = {
    AgentStatus.OFFLINE: [AgentStatus.INITIALIZING],
    AgentStatus.INITIALIZING: [AgentStatus.IDLE, AgentStatus.ERROR],
    AgentStatus.IDLE: [AgentStatus.WORKING, AgentStatus.SHUTTING_DOWN, AgentStatus.ERROR],
    AgentStatus.WORKING: [AgentStatus.IDLE, AgentStatus.WAITING, AgentStatus.ERROR],
    AgentStatus.WAITING: [AgentStatus.WORKING, AgentStatus.IDLE, AgentStatus.ERROR],
    AgentStatus.ERROR: [AgentStatus.IDLE, AgentStatus.SHUTTING_DOWN],
    AgentStatus.SHUTTING_DOWN: [AgentStatus.OFFLINE],
}

TASK_STATUS_TRANSITIONS = {
    TaskStatus.PENDING: [TaskStatus.ASSIGNED, TaskStatus.CANCELLED],
    TaskStatus.ASSIGNED: [TaskStatus.IN_PROGRESS, TaskStatus.CANCELLED],
    TaskStatus.IN_PROGRESS: [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.BLOCKED, TaskStatus.CANCELLED],
    TaskStatus.BLOCKED: [TaskStatus.IN_PROGRESS, TaskStatus.CANCELLED],
    TaskStatus.COMPLETED: [TaskStatus.REVIEW],
    TaskStatus.REVIEW: [TaskStatus.APPROVED, TaskStatus.REJECTED],
    TaskStatus.APPROVED: [],  # Terminal state
    TaskStatus.REJECTED: [TaskStatus.ASSIGNED],
    TaskStatus.FAILED: [TaskStatus.ASSIGNED, TaskStatus.CANCELLED],
    TaskStatus.CANCELLED: [],  # Terminal state
}

# Priority ordering (higher number = higher priority)
PRIORITY_ORDER = {
    TaskPriority.LOW: 1,
    TaskPriority.MEDIUM: 2,
    TaskPriority.HIGH: 3,
    TaskPriority.URGENT: 4,
    TaskPriority.CRITICAL: 5,
}


def can_transition_agent_status(from_status: AgentStatus, to_status: AgentStatus) -> bool:
    """Check if an agent status transition is valid."""
    return to_status in AGENT_STATUS_TRANSITIONS.get(from_status, [])


def can_transition_task_status(from_status: TaskStatus, to_status: TaskStatus) -> bool:
    """Check if a task status transition is valid."""
    return to_status in TASK_STATUS_TRANSITIONS.get(from_status, [])


def get_priority_value(priority: TaskPriority) -> int:
    """Get numeric value for task priority."""
    return PRIORITY_ORDER.get(priority, 0)


def compare_priorities(priority1: TaskPriority, priority2: TaskPriority) -> int:
    """Compare two priorities. Returns -1, 0, or 1."""
    val1 = get_priority_value(priority1)
    val2 = get_priority_value(priority2)
    
    if val1 < val2:
        return -1
    elif val1 > val2:
        return 1
    else:
        return 0
